<script setup lang="ts">
import {
  Alignment,
  AutoImage,
  Autoformat,
  Autosave,
  BlockQuote,
  Bold,
  Essentials,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  Heading,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  InlineEditor,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  Markdown,
  MediaEmbed,
  Mention,
  Paragraph,
  PasteFromMarkdownExperimental,
  PasteFromOffice,
  SimpleUploadAdapter,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  // TableLayout,
  TableProperties,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
} from 'ckeditor5'
import { Ckeditor } from '@ckeditor/ckeditor5-vue'
import 'ckeditor5/ckeditor5.css'
import translations from 'ckeditor5/translations/zh.js'
import Math from '@isaul32/ckeditor5-math/src/math'
import AutoformatMath from '@isaul32/ckeditor5-math/src/autoformatmath'
import { nanoid } from '@sa/utils'

defineOptions({
  name: 'CKEditor',
})

const data = ref('')
const LICENSE_KEY = 'GPL' // or <YOUR_LICENSE_KEY>.
const isLayoutReady = ref(false)
const editor = InlineEditor

// 确保编辑器实例的唯一性
const editorId = `ckeditor-${nanoid()}`

const config = computed(() => {
  if (!isLayoutReady.value) {
    return null
  }

  return {
    toolbar: {
      items: [
        'fontFamily',
        'fontSize',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'subscript',
        'superscript',
        '|',
        'alignment',
        'outdent',
        'indent',
        '|',
        'math',
        'insertImage',
        'insertTable',
        'specialCharacters',

      ],
      shouldNotGroupWhenFull: false,
    },
    plugins: [
      Math,
      AutoformatMath,
      Alignment,
      Autoformat,
      AutoImage,
      Autosave,
      BlockQuote,
      Bold,
      Essentials,
      FontBackgroundColor,
      FontColor,
      FontFamily,
      FontSize,
      Heading,
      ImageBlock,
      ImageCaption,
      ImageInline,
      ImageInsert,
      ImageInsertViaUrl,
      ImageResize,
      ImageStyle,
      ImageTextAlternative,
      ImageToolbar,
      ImageUpload,
      Indent,
      IndentBlock,
      Italic,
      Link,
      LinkImage,
      List,
      ListProperties,
      Markdown,
      MediaEmbed,
      Mention,
      Paragraph,
      PasteFromMarkdownExperimental,
      PasteFromOffice,
      SimpleUploadAdapter,
      SpecialCharacters,
      SpecialCharactersArrows,
      SpecialCharactersCurrency,
      SpecialCharactersEssentials,
      SpecialCharactersLatin,
      SpecialCharactersMathematical,
      SpecialCharactersText,
      Strikethrough,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      // TableLayout,
      TableProperties,
      TableToolbar,
      TextTransformation,
      TodoList,
      Underline,
    ],
    fontFamily: {
      supportAllValues: true,
    },
    fontSize: {
      options: [10, 12, 14, 'default', 18, 20, 22],
      supportAllValues: true,
    },

    heading: {
      options: [
        {
          model: 'paragraph' as const,
          view: 'p' as const,
          title: '测试段落',
          class: 'ck-heading_paragraph',
        },
        {
          model: 'heading1' as const,
          view: 'h1' as const,
          title: 'Heading 1',
          class: 'ck-heading_heading1',
        },
        {
          model: 'heading2' as const,
          view: 'h2' as const,
          title: 'Heading 2',
          class: 'ck-heading_heading2',
        },
        {
          model: 'heading3' as const,
          view: 'h3' as const,
          title: 'Heading 3',
          class: 'ck-heading_heading3',
        },
        {
          model: 'heading4' as const,
          view: 'h4' as const,
          title: 'Heading 4',
          class: 'ck-heading_heading4',
        },
        {
          model: 'heading5' as const,
          view: 'h5' as const,
          title: 'Heading 5',
          class: 'ck-heading_heading5',
        },
        {
          model: 'heading6' as const,
          view: 'h6' as const,
          title: 'Heading 6',
          class: 'ck-heading_heading6',
        },
      ],
    },
    image: {
      toolbar: [
        'toggleImageCaption',
        'imageTextAlternative',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
      ],
    },
    initialData: '',
    language: 'zh',
    licenseKey: LICENSE_KEY,
    link: {
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://',
      decorators: {
        toggleDownloadable: {
          mode: 'manual' as const,
          label: 'Downloadable',
          attributes: {
            download: 'file',
          },
        },
      },
    },
    list: {
      properties: {
        styles: true,
        startIndex: true,
        reversed: true,
      },
    },
    mention: {
      feeds: [
        {
          marker: '@',
          feed: [
            /* See: https://ckeditor.com/docs/ckeditor5/latest/features/mentions.html */
          ],
        },
      ],
    },
    placeholder: '',
    table: {
      contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties'],
    },
    translations: [translations],
  }
})
onMounted(() => {
  isLayoutReady.value = true
})
</script>

<template>
  <div :id="editorId">
    <Ckeditor
      v-if="editor && config"
      v-model="data"
      class="question-item"
      :editor="editor"
      :config="config"
    />
  </div>
</template>

<style>
.ck.ck-editor__editable_inline {
    border: 1px solid #d9d9d9!important;
    padding: 3px 8px!important;
}

.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable) {
    border: 1px solid #386bff!important;
    -webkit-box-shadow: inset 2px 2px 3px rgba(0,0,0,.1)!important;
    box-shadow: inset 2px 2px 3px #0000001a!important;
    outline: none
}
.ck.ck-balloon-panel.ck-powered-by-balloon {
  display: none!important;
}
.question-item {
    -webkit-box-flex: 1;
    background-color: #fff;
    border: 1px solid var(--border,#d9d9d9);
    border-radius: 4px!important;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #333;
    -ms-flex: 1;
    flex: 1;
    margin-left: 8px;
    min-height: 32px;
    padding: 3px 8px;
    word-break: break-word
}

 .question-item p {
    margin-bottom: 0!important;
    margin-top: 0!important
}

 .question-item-textarea {
    color: #333;
    font-family: inherit;
    font-size: 14px;
    height: 100px;
    line-height: 24px;
    outline: none
}

 .question-item-textarea:focus {
    border: 1px solid #386bff
}
</style>
